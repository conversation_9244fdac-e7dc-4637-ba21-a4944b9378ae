import React from 'react';
import { Link } from 'react-router-dom';
import styles from './Footer.module.css';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.footerContent}>
          {/* Brand Section */}
          <div className={styles.brandSection}>
            <Link to="/" className={styles.brandLink}>
              <h3 className={styles.brandName}>Apothecary Extracts</h3>
            </Link>
            <p className={styles.brandDescription}>
              Premium cannabis extracts and products crafted with precision and care. 
              Serving Massachusetts with quality you can trust.
            </p>
            <div className={styles.socialLinks}>
              <a 
                href="https://facebook.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className={styles.socialLink}
                aria-label="Follow us on Facebook"
              >
                Facebook
              </a>
              <a 
                href="https://instagram.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className={styles.socialLink}
                aria-label="Follow us on Instagram"
              >
                Instagram
              </a>
              <a 
                href="https://youtube.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className={styles.socialLink}
                aria-label="Subscribe to our YouTube channel"
              >
                YouTube
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className={styles.linkSection}>
            <h4 className={styles.sectionTitle}>Quick Links</h4>
            <ul className={styles.linkList}>
              <li><Link to="/about" className={styles.footerLink}>About Us</Link></li>
              <li><Link to="/blog" className={styles.footerLink}>Blog</Link></li>
              <li><Link to="/locations" className={styles.footerLink}>Find Us</Link></li>
              <li><Link to="/newsletter" className={styles.footerLink}>Newsletter</Link></li>
              <li><Link to="/faqs" className={styles.footerLink}>FAQs</Link></li>
            </ul>
          </div>

          {/* Products */}
          <div className={styles.linkSection}>
            <h4 className={styles.sectionTitle}>Products</h4>
            <ul className={styles.linkList}>
              <li><Link to="/shop/flower" className={styles.footerLink}>Flower</Link></li>
              <li><Link to="/shop/pre-rolls" className={styles.footerLink}>Pre-rolls</Link></li>
              <li><Link to="/shop/edibles" className={styles.footerLink}>Edibles</Link></li>
              <li><Link to="/shop/extracts" className={styles.footerLink}>Extracts</Link></li>
              <li><Link to="/shop/tinctures" className={styles.footerLink}>Tinctures</Link></li>
              <li><Link to="/shop/vapes" className={styles.footerLink}>Vapes</Link></li>
            </ul>
          </div>

          {/* Locations */}
          <div className={styles.linkSection}>
            <h4 className={styles.sectionTitle}>Locations</h4>
            <ul className={styles.linkList}>
              <li><Link to="/locations/hadley" className={styles.footerLink}>Hadley Dispensary</Link></li>
              <li><Link to="/locations/bernardston" className={styles.footerLink}>Bernardston Dispensary</Link></li>
            </ul>
            
            <div className={styles.appDownload}>
              <h5 className={styles.appTitle}>Download Our App</h5>
              <div className={styles.appButtons}>
                <a 
                  href="https://apps.apple.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className={styles.appButton}
                >
                  App Store
                </a>
                <a 
                  href="https://play.google.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className={styles.appButton}
                >
                  Google Play
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className={styles.bottomSection}>
          <div className={styles.legalLinks}>
            <Link to="/privacy-policy" className={styles.legalLink}>Privacy Policy</Link>
            <Link to="/terms-of-use" className={styles.legalLink}>Terms of Use</Link>
          </div>
          
          <div className={styles.copyright}>
            <p>&copy; {currentYear} Apothecary Extracts. All rights reserved.</p>
          </div>
          
          <div className={styles.disclaimer}>
            <p>
              Cannabis products have not been evaluated by the FDA. 
              Keep out of reach of children and pets.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
