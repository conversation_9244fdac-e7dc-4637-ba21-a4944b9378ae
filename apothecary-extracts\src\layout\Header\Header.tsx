import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ASCIIText from '../../components/ASCIIText';
import NavLink from '../../components/NavLink';
import styles from './Header.module.css';

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className={`${styles.header} ${className || ''}`}>
      <div className={styles.container}>
        <div className={styles.headerContent}>
          {/* Logo */}
          <Link to="/" className={styles.logo} onClick={closeMenu}>
            <div className={styles.logoContainer}>
              <ASCIIText
                text="AE"
                asciiFontSize={6}
                textFontSize={120}
                textColor="#ffffff"
                planeBaseHeight={4}
                enableWaves={true}
              />
            </div>
            <span className={styles.tagline}>Premium Cannabis Products</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className={styles.desktopNav} aria-label="Main navigation">
            <ul className={styles.navList}>
              <li><NavLink to="/shop">Shop</NavLink></li>
              <li><NavLink to="/about">About</NavLink></li>
              <li><NavLink to="/locations">Find Us</NavLink></li>
              <li><NavLink to="/blog">Blog</NavLink></li>
              <li><NavLink to="/newsletter">Newsletter</NavLink></li>
            </ul>
          </nav>

          {/* Mobile Menu Button */}
          <button
            className={styles.menuButton}
            onClick={toggleMenu}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
            aria-label="Toggle navigation menu"
          >
            <span className={styles.menuIcon}>
              <span className={`${styles.menuLine} ${isMenuOpen ? styles.menuLineOpen : ''}`}></span>
              <span className={`${styles.menuLine} ${isMenuOpen ? styles.menuLineOpen : ''}`}></span>
              <span className={`${styles.menuLine} ${isMenuOpen ? styles.menuLineOpen : ''}`}></span>
            </span>
            <span className={styles.menuText}>Menu</span>
          </button>
        </div>

        {/* Mobile Navigation */}
        <nav 
          id="mobile-menu"
          className={`${styles.mobileNav} ${isMenuOpen ? styles.mobileNavOpen : ''}`}
          aria-label="Mobile navigation"
        >
          <ul className={styles.mobileNavList}>
            <li>
              <NavLink to="/shop" onClick={closeMenu} className={styles.mobileNavLink}>
                Shop
              </NavLink>
            </li>
            <li>
              <NavLink to="/about" onClick={closeMenu} className={styles.mobileNavLink}>
                About
              </NavLink>
            </li>
            <li>
              <NavLink to="/locations" onClick={closeMenu} className={styles.mobileNavLink}>
                Find Us
              </NavLink>
            </li>
            <li>
              <NavLink to="/blog" onClick={closeMenu} className={styles.mobileNavLink}>
                Blog
              </NavLink>
            </li>
            <li>
              <NavLink to="/newsletter" onClick={closeMenu} className={styles.mobileNavLink}>
                Newsletter
              </NavLink>
            </li>
          </ul>
        </nav>
      </div>

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div 
          className={styles.mobileOverlay}
          onClick={closeMenu}
          aria-hidden="true"
        />
      )}
    </header>
  );
};

export default Header;
