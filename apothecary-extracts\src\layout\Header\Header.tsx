import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styles from './Header.module.css';

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className={`${styles.header} ${className || ''}`}>
      <div className={styles.container}>
        <div className={styles.headerContent}>
          {/* Logo */}
          <Link to="/" className={styles.logo} onClick={closeMenu}>
            <h1 className={styles.brandName}>Apothecary Extracts</h1>
            <span className={styles.tagline}>Premium Cannabis Products</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className={styles.desktopNav} aria-label="Main navigation">
            <ul className={styles.navList}>
              <li><Link to="/shop" className={styles.navLink}>Shop</Link></li>
              <li><Link to="/about" className={styles.navLink}>About</Link></li>
              <li><Link to="/locations" className={styles.navLink}>Find Us</Link></li>
              <li><Link to="/blog" className={styles.navLink}>Blog</Link></li>
              <li><Link to="/newsletter" className={styles.navLink}>Newsletter</Link></li>
            </ul>
          </nav>

          {/* Mobile Menu Button */}
          <button
            className={styles.menuButton}
            onClick={toggleMenu}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
            aria-label="Toggle navigation menu"
          >
            <span className={styles.menuIcon}>
              <span className={`${styles.menuLine} ${isMenuOpen ? styles.menuLineOpen : ''}`}></span>
              <span className={`${styles.menuLine} ${isMenuOpen ? styles.menuLineOpen : ''}`}></span>
              <span className={`${styles.menuLine} ${isMenuOpen ? styles.menuLineOpen : ''}`}></span>
            </span>
            <span className={styles.menuText}>Menu</span>
          </button>
        </div>

        {/* Mobile Navigation */}
        <nav 
          id="mobile-menu"
          className={`${styles.mobileNav} ${isMenuOpen ? styles.mobileNavOpen : ''}`}
          aria-label="Mobile navigation"
        >
          <ul className={styles.mobileNavList}>
            <li>
              <Link to="/shop" className={styles.mobileNavLink} onClick={closeMenu}>
                Shop
              </Link>
            </li>
            <li>
              <Link to="/about" className={styles.mobileNavLink} onClick={closeMenu}>
                About
              </Link>
            </li>
            <li>
              <Link to="/locations" className={styles.mobileNavLink} onClick={closeMenu}>
                Find Us
              </Link>
            </li>
            <li>
              <Link to="/blog" className={styles.mobileNavLink} onClick={closeMenu}>
                Blog
              </Link>
            </li>
            <li>
              <Link to="/newsletter" className={styles.mobileNavLink} onClick={closeMenu}>
                Newsletter
              </Link>
            </li>
          </ul>
        </nav>
      </div>

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div 
          className={styles.mobileOverlay}
          onClick={closeMenu}
          aria-hidden="true"
        />
      )}
    </header>
  );
};

export default Header;
