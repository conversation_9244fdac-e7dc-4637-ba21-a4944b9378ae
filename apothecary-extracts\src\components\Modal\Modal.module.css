/* Modal Component Styles */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--space-md);
  animation: overlayFadeIn 0.2s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal {
  background: var(--color-background-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal sizes */
.small {
  width: 100%;
  max-width: 400px;
}

.medium {
  width: 100%;
  max-width: 600px;
}

.large {
  width: 100%;
  max-width: 800px;
}

.fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg) var(--space-lg) 0;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: var(--space-lg);
}

.title {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  flex: 1;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
  color: var(--color-text-secondary);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--space-md);
}

.closeButton:hover {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.closeButton:focus {
  outline: 2px solid var(--color-primary-brand);
  outline-offset: 2px;
}

/* Content */
.content {
  padding: 0 var(--space-lg) var(--space-lg);
}

/* When there's no header */
.modal:not(:has(.header)) .content {
  padding: var(--space-lg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .overlay {
    padding: var(--space-sm);
  }
  
  .modal {
    max-height: 95vh;
  }
  
  .header {
    padding: var(--space-md) var(--space-md) 0;
    margin-bottom: var(--space-md);
  }
  
  .content {
    padding: 0 var(--space-md) var(--space-md);
  }
  
  .modal:not(:has(.header)) .content {
    padding: var(--space-md);
  }
  
  .title {
    font-size: var(--font-size-h5);
  }
}

@media (max-width: 480px) {
  .small,
  .medium,
  .large {
    width: 100%;
    max-width: none;
    margin: 0;
  }
  
  .modal {
    border-radius: var(--border-radius-md);
  }
}
