import { useRef, useEffect } from "react";
import * as THREE from "three";

interface ASCIITextProps {
  text?: string;
  enableWaves?: boolean;
  asciiFontSize?: number;
  textFontSize?: number;
  planeBaseHeight?: number;
  textColor?: string;
  strokeColor?: string;
}

const vertexShader = `
varying vec2 vUv;
uniform float uTime;
uniform float mouse;
uniform float uEnableWaves;

void main() {
  vUv = uv;
  float time = uTime * 5.;

  float waveFactor = uEnableWaves;

  vec3 transformed = position;
  transformed.x += sin(time + position.y) * 0.5 * waveFactor;
  transformed.y += cos(time + position.z) * 0.15 * waveFactor;
  transformed.z += sin(time + position.x) * waveFactor;

  gl_Position = projectionMatrix * modelViewMatrix * vec4(transformed, 1.0);
}
`;

const fragmentShader = `
varying vec2 vUv;
uniform float mouse;
uniform float uTime;
uniform sampler2D uTexture;

void main() {
  float time = uTime;
  vec2 pos = vUv;

  float move = sin(time + mouse) * 0.01;
  float r = texture2D(uTexture, pos + cos(time * 2. - time + pos.x) * .01).r;
  float g = texture2D(uTexture, pos + tan(time * .5 + pos.x - time) * .01).g;
  float b = texture2D(uTexture, pos - cos(time * 2. + time + pos.y) * .01).b;
  float a = texture2D(uTexture, pos).a;
  gl_FragColor = vec4(r, g, b, a);
}
`;

// Extend Math interface for the map function
declare global {
  interface Math {
    map(n: number, start: number, stop: number, start2: number, stop2: number): number;
  }
}

Math.map = function (n: number, start: number, stop: number, start2: number, stop2: number) {
  return ((n - start) / (stop - start)) * (stop2 - start2) + start2;
};

const PX_RATIO = typeof window !== "undefined" ? window.devicePixelRatio : 1;

class AsciiFilter {
  renderer: THREE.WebGLRenderer;
  domElement: HTMLDivElement;
  oAsciiArt: HTMLPreElement;
  iWidth: number;
  iHeight: number;
  oImgData: ImageData;
  aCharList: string[];
  aRGB: number[];
  strFont: string;
  fFontsize: number;
  fFontOffset: number;
  bInvert: boolean;

  constructor(renderer: THREE.WebGLRenderer, { fontSize, fontFamily, charset, invert }: any = {}) {
    this.renderer = renderer;
    this.domElement = document.createElement('div');
    this.domElement.style.position = 'absolute';
    this.domElement.style.top = '0';
    this.domElement.style.left = '0';
    this.domElement.style.width = '100%';
    this.domElement.style.height = '100%';
    this.domElement.style.overflow = 'hidden';
    this.domElement.style.fontFamily = fontFamily || 'IBM Plex Mono, monospace';
    this.domElement.style.fontSize = (fontSize || 12) + 'px';
    this.domElement.style.lineHeight = '1em';
    this.domElement.style.color = '#ffffff';
    this.domElement.style.backgroundColor = 'transparent';
    this.domElement.style.pointerEvents = 'none';

    this.oAsciiArt = document.createElement('pre');
    this.oAsciiArt.style.margin = '0';
    this.oAsciiArt.style.padding = '0';
    this.oAsciiArt.style.whiteSpace = 'pre';
    this.oAsciiArt.style.fontFamily = 'inherit';
    this.oAsciiArt.style.fontSize = 'inherit';
    this.oAsciiArt.style.lineHeight = 'inherit';
    this.oAsciiArt.style.color = 'inherit';
    this.domElement.appendChild(this.oAsciiArt);

    this.iWidth = 0;
    this.iHeight = 0;
    this.oImgData = new ImageData(1, 1);
    this.aCharList = charset || [' ', '.', ':', '-', '=', '+', '*', '#', '%', '@'];
    this.aRGB = [];
    this.strFont = fontFamily || 'IBM Plex Mono, monospace';
    this.fFontsize = fontSize || 12;
    this.fFontOffset = 0;
    this.bInvert = invert || false;
  }

  setSize(width: number, height: number) {
    this.iWidth = Math.floor(width / this.fFontsize * PX_RATIO);
    this.iHeight = Math.floor(height / this.fFontsize * PX_RATIO);
  }

  render(scene: THREE.Scene, camera: THREE.Camera) {
    const canvas = this.renderer.domElement;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    this.renderer.render(scene, camera);

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    this.oImgData = imageData;

    let strChars = '';
    for (let y = 0; y < this.iHeight; y++) {
      for (let x = 0; x < this.iWidth; x++) {
        const iOffset = (y * this.iWidth + x) * 4;
        const iRed = imageData.data[iOffset];
        const iGreen = imageData.data[iOffset + 1];
        const iBlue = imageData.data[iOffset + 2];
        const iAlpha = imageData.data[iOffset + 3];

        if (iAlpha === 0) {
          strChars += ' ';
        } else {
          const fBrightness = (0.3 * iRed + 0.59 * iGreen + 0.11 * iBlue) / 255;
          const iCharIdx = Math.floor((this.bInvert ? 1 - fBrightness : fBrightness) * (this.aCharList.length - 1));
          strChars += this.aCharList[iCharIdx];
        }
      }
      strChars += '\n';
    }

    this.oAsciiArt.textContent = strChars;
  }

  dispose() {
    if (this.domElement.parentNode) {
      this.domElement.parentNode.removeChild(this.domElement);
    }
  }
}

class CanvAscii {
  text: string;
  asciiFontSize: number;
  textFontSize: number;
  textColor: string;
  planeBaseHeight: number;
  enableWaves: boolean;
  container: HTMLElement;
  width: number;
  height: number;
  renderer?: THREE.WebGLRenderer;
  scene?: THREE.Scene;
  camera?: THREE.PerspectiveCamera;
  plane?: THREE.Mesh;
  asciiFilter?: AsciiFilter;
  clock?: THREE.Clock;

  constructor(
    { text, asciiFontSize, textFontSize, textColor, planeBaseHeight, enableWaves }: any,
    container: HTMLElement,
    width: number,
    height: number
  ) {
    this.text = text;
    this.asciiFontSize = asciiFontSize;
    this.textFontSize = textFontSize;
    this.textColor = textColor;
    this.planeBaseHeight = planeBaseHeight;
    this.enableWaves = enableWaves;
    this.container = container;
    this.width = width;
    this.height = height;
  }

  async load() {
    // Initialize Three.js
    this.renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
    this.renderer.setSize(this.width, this.height);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.setClearColor(0x000000, 0);

    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, this.width / this.height, 0.1, 1000);
    this.camera.position.z = 10;

    this.clock = new THREE.Clock();

    // Create text texture
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    canvas.width = 1024;
    canvas.height = 512;

    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.font = `${this.textFontSize}px Arial, sans-serif`;
    ctx.fillStyle = this.textColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(this.text, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // Create plane geometry
    const aspectRatio = canvas.width / canvas.height;
    const planeWidth = this.planeBaseHeight * aspectRatio;
    const geometry = new THREE.PlaneGeometry(planeWidth, this.planeBaseHeight, 32, 32);

    // Create material with shaders
    const material = new THREE.ShaderMaterial({
      uniforms: {
        uTexture: { value: texture },
        uTime: { value: 0 },
        mouse: { value: 0 },
        uEnableWaves: { value: this.enableWaves ? 1.0 : 0.0 },
      },
      vertexShader,
      fragmentShader,
      transparent: true,
    });

    this.plane = new THREE.Mesh(geometry, material);
    this.scene.add(this.plane);

    // Create ASCII filter
    this.asciiFilter = new AsciiFilter(this.renderer, {
      fontSize: this.asciiFontSize,
      fontFamily: 'IBM Plex Mono, monospace',
      charset: [' ', '.', ':', '-', '=', '+', '*', '#', '%', '@'],
      invert: false,
    });

    this.asciiFilter.setSize(this.width, this.height);
    this.container.appendChild(this.asciiFilter.domElement);

    // Start animation loop
    this.animate();
  }

  animate = () => {
    if (!this.renderer || !this.scene || !this.camera || !this.plane || !this.asciiFilter || !this.clock) return;

    const time = this.clock.getElapsedTime();

    // Update shader uniforms
    const material = this.plane.material as THREE.ShaderMaterial;
    material.uniforms.uTime.value = time;

    // Render to ASCII
    this.asciiFilter.render(this.scene, this.camera);

    requestAnimationFrame(this.animate);
  };

  setSize(width: number, height: number) {
    this.width = width;
    this.height = height;

    if (this.renderer) {
      this.renderer.setSize(width, height);
    }

    if (this.camera) {
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
    }

    if (this.asciiFilter) {
      this.asciiFilter.setSize(width, height);
    }
  }

  dispose() {
    if (this.renderer) {
      this.renderer.dispose();
    }
    if (this.asciiFilter) {
      this.asciiFilter.dispose();
    }
  }
}

export default function ASCIIText({
  text = "David!",
  asciiFontSize = 8,
  textFontSize = 200,
  textColor = "#fdf9f3",
  planeBaseHeight = 8,
  enableWaves = true,
}: ASCIITextProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const asciiRef = useRef<any>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const { width, height } = containerRef.current.getBoundingClientRect();

    if (width === 0 || height === 0) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && entry.boundingClientRect.width > 0 && entry.boundingClientRect.height > 0) {
            const { width: w, height: h } = entry.boundingClientRect;

            asciiRef.current = new CanvAscii(
              { text, asciiFontSize, textFontSize, textColor, planeBaseHeight, enableWaves },
              containerRef.current!,
              w,
              h
            );
            asciiRef.current.load();
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      observer.observe(containerRef.current);

      return () => {
        observer.disconnect();
        if (asciiRef.current) {
          asciiRef.current.dispose();
        }
      };
    }

    // Direct instantiation when container has dimensions
    asciiRef.current = new CanvAscii(
      { text, asciiFontSize, textFontSize, textColor, planeBaseHeight, enableWaves },
      containerRef.current,
      width,
      height
    );
    asciiRef.current.load();

    // Handle resize
    const handleResize = () => {
      if (asciiRef.current && containerRef.current) {
        const { width: w, height: h } = containerRef.current.getBoundingClientRect();
        asciiRef.current.setSize(w, h);
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (asciiRef.current) {
        asciiRef.current.dispose();
      }
    };
  }, [text, asciiFontSize, textFontSize, textColor, planeBaseHeight, enableWaves]);

  return (
    <div
      ref={containerRef}
      className="ascii-text-container"
      style={{
        position: "absolute",
        width: "100%",
        height: "100%",
      }}
    >
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@500&display=swap');
        
        .ascii-text-container canvas {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          image-rendering: optimizeSpeed;
          image-rendering: -moz-crisp-edges;
          image-rendering: -o-crisp-edges;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: optimize-contrast;
          image-rendering: crisp-edges;
          image-rendering: pixelated;
        }
        
        .ascii-text-container pre {
          margin: 0;
          user-select: none;
          padding: 0;
          line-height: 1em;
          text-align: left;
          position: absolute;
          left: 0;
          top: 0;
          background-image: radial-gradient(circle, #ff6188 0%, #fc9867 50%, #ffd866 100%);
          background-attachment: fixed;
          -webkit-text-fill-color: transparent;
          -webkit-background-clip: text;
          z-index: 9;
          mix-blend-mode: difference;
        }
      `}</style>
    </div>
  );
}
