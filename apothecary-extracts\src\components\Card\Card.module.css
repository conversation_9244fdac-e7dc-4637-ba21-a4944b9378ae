/* Card Component Styles */

.card {
  background-color: var(--color-background-primary);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  display: block;
  width: 100%;
  text-align: left;
  border: none;
  font-family: inherit;
  color: inherit;
}

/* Variants */
.default {
  background-color: var(--color-background-secondary);
  box-shadow: var(--shadow-sm);
}

.elevated {
  background-color: var(--color-background-primary);
  box-shadow: var(--shadow-md);
}

.outlined {
  background-color: var(--color-background-primary);
  border: 1px solid var(--color-border);
  box-shadow: none;
}

/* Padding variants */
.padding-none {
  padding: 0;
}

.padding-small {
  padding: var(--space-md);
}

.padding-medium {
  padding: var(--space-lg);
}

.padding-large {
  padding: var(--space-xl);
}

/* Clickable state */
.clickable {
  cursor: pointer;
  user-select: none;
}

.clickable:hover {
  transform: translateY(-2px);
}

.clickable.default:hover {
  box-shadow: var(--shadow-md);
}

.clickable.elevated:hover {
  box-shadow: var(--shadow-lg);
}

.clickable.outlined:hover {
  border-color: var(--color-primary-brand);
  box-shadow: var(--shadow-sm);
}

.clickable:focus {
  outline: 2px solid var(--color-primary-brand);
  outline-offset: 2px;
}

.clickable:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .padding-large {
    padding: var(--space-lg);
  }
  
  .padding-medium {
    padding: var(--space-md);
  }
}
