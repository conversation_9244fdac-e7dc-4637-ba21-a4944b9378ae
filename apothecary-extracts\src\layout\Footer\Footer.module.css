/* Footer Styles */

.footer {
  background-color: var(--color-primary-brand);
  color: white;
  margin-top: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-xxl) var(--space-md) var(--space-lg);
}

.footerContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-xl);
  margin-bottom: var(--space-xl);
}

/* Brand Section */
.brandSection {
  text-align: center;
}

.brandLink {
  text-decoration: none;
  color: inherit;
}

.brandLink:hover {
  text-decoration: none;
}

.brandName {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-bold);
  color: white;
  margin-bottom: var(--space-md);
}

.brandDescription {
  font-size: var(--font-size-base);
  line-height: var(--line-height-body);
  margin-bottom: var(--space-lg);
  opacity: 0.9;
}

.socialLinks {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.socialLink {
  color: white;
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) var(--space-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
}

.socialLink:hover,
.socialLink:focus {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  text-decoration: none;
  color: white;
}

/* Link Sections */
.linkSection {
  text-align: center;
}

.sectionTitle {
  font-size: var(--font-size-h6);
  font-weight: var(--font-weight-semibold);
  color: white;
  margin-bottom: var(--space-md);
}

.linkList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.linkList li {
  margin-bottom: var(--space-sm);
}

.footerLink {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.footerLink:hover,
.footerLink:focus {
  color: white;
  text-decoration: none;
}

/* App Download */
.appDownload {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.appTitle {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: white;
  margin-bottom: var(--space-md);
}

.appButtons {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  align-items: center;
}

.appButton {
  color: white;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) var(--space-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  min-width: 120px;
  text-align: center;
}

.appButton:hover,
.appButton:focus {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  text-decoration: none;
  color: white;
}

/* Bottom Section */
.bottomSection {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--space-lg);
  text-align: center;
}

.legalLinks {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
  flex-wrap: wrap;
}

.legalLink {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.legalLink:hover,
.legalLink:focus {
  color: white;
  text-decoration: underline;
}

.copyright {
  margin-bottom: var(--space-md);
}

.copyright p {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.disclaimer p {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: var(--line-height-body);
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-xxl) var(--space-lg) var(--space-lg);
  }
  
  .footerContent {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--space-xxl);
  }
  
  .brandSection,
  .linkSection {
    text-align: left;
  }
  
  .socialLinks {
    justify-content: flex-start;
  }
  
  .appButtons {
    flex-direction: row;
    justify-content: flex-start;
  }
  
  .bottomSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }
  
  .legalLinks {
    margin-bottom: 0;
  }
  
  .disclaimer {
    max-width: 300px;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-xxl) var(--space-xl) var(--space-lg);
  }
}
