import { useState, useEffect } from 'react';

const AGE_VERIFICATION_KEY = 'apothecary_age_verified';
const VERIFICATION_EXPIRY_HOURS = 24; // Age verification expires after 24 hours

interface AgeVerificationState {
  isVerified: boolean;
  showModal: boolean;
  verifyAge: () => void;
  denyAge: () => void;
}

export const useAgeVerification = (): AgeVerificationState => {
  const [isVerified, setIsVerified] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Check if user has already verified their age
    const checkAgeVerification = () => {
      try {
        const stored = localStorage.getItem(AGE_VERIFICATION_KEY);
        if (stored) {
          const { timestamp, verified } = JSON.parse(stored);
          const now = new Date().getTime();
          const expiryTime = timestamp + (VERIFICATION_EXPIRY_HOURS * 60 * 60 * 1000);
          
          if (verified && now < expiryTime) {
            setIsVerified(true);
            setShowModal(false);
            return;
          } else {
            // Verification expired, remove from storage
            localStorage.removeItem(AGE_VERIFICATION_KEY);
          }
        }
      } catch (error) {
        console.error('Error checking age verification:', error);
        localStorage.removeItem(AGE_VERIFICATION_KEY);
      }
      
      // Show modal if not verified or verification expired
      setIsVerified(false);
      setShowModal(true);
    };

    checkAgeVerification();
  }, []);

  const verifyAge = () => {
    try {
      const verificationData = {
        verified: true,
        timestamp: new Date().getTime()
      };
      localStorage.setItem(AGE_VERIFICATION_KEY, JSON.stringify(verificationData));
      setIsVerified(true);
      setShowModal(false);
    } catch (error) {
      console.error('Error storing age verification:', error);
      // Still allow access even if localStorage fails
      setIsVerified(true);
      setShowModal(false);
    }
  };

  const denyAge = () => {
    // Redirect to a safe external site
    window.location.href = 'https://www.google.com';
  };

  return {
    isVerified,
    showModal,
    verifyAge,
    denyAge
  };
};
