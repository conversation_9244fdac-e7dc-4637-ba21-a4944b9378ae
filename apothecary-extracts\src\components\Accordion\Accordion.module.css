/* Accordion Component Styles */

.accordion {
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.accordionItem {
  border-bottom: 1px solid var(--color-border);
}

.accordionItem:last-child {
  border-bottom: none;
}

.accordionHeader {
  width: 100%;
  padding: var(--space-lg);
  background-color: var(--color-background-secondary);
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-md);
  transition: background-color var(--transition-fast);
  font-family: inherit;
  font-size: var(--font-size-base);
}

.accordionHeader:hover {
  background-color: var(--color-background-primary);
}

.accordionHeader:focus {
  outline: 2px solid var(--color-primary-brand);
  outline-offset: -2px;
  background-color: var(--color-background-primary);
}

.accordionTitle {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  flex: 1;
}

.accordionIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  transition: transform var(--transition-fast);
  flex-shrink: 0;
}

.accordionIconOpen {
  transform: rotate(180deg);
}

.accordionContent {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal) ease-out;
  background-color: var(--color-background-primary);
}

.accordionContentOpen {
  max-height: 1000px; /* Large enough value for content */
  transition: max-height var(--transition-normal) ease-in;
}

.accordionContentInner {
  padding: var(--space-lg);
  color: var(--color-text-primary);
  line-height: var(--line-height-body);
}

.accordionContentInner p {
  margin-bottom: var(--space-md);
}

.accordionContentInner p:last-child {
  margin-bottom: 0;
}

.accordionContentInner ul,
.accordionContentInner ol {
  margin-bottom: var(--space-md);
  padding-left: var(--space-lg);
}

.accordionContentInner li {
  margin-bottom: var(--space-xs);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .accordionHeader {
    padding: var(--space-md);
  }
  
  .accordionContentInner {
    padding: var(--space-md);
  }
}
