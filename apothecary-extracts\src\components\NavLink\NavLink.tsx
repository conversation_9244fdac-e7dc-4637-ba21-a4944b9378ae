import React from 'react';
import { Link } from 'react-router-dom';
import './NavLink.css';

interface NavLinkProps {
  to: string;
  children: string;
  onClick?: () => void;
  className?: string;
}

const NavLink: React.FC<NavLinkProps> = ({ to, children, onClick, className }) => {
  return (
    <Link to={to} onClick={onClick} className={`nav-link-wrapper ${className || ''}`}>
      <div className="nav-link-text-trail">
        <span style={{
          color: '#ffffff',
          fontWeight: 600,
          transition: 'all 0.3s ease',
          textShadow: '0 0 10px rgba(255, 255, 255, 0.5)'
        }}>
          {children}
        </span>
      </div>
    </Link>
  );
};

export default NavLink;
