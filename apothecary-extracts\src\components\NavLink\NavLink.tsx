import React from 'react';
import { Link } from 'react-router-dom';
import TextTrail from '../TextTrail';
import './NavLink.css';

interface NavLinkProps {
  to: string;
  children: string;
  onClick?: () => void;
  className?: string;
}

const NavLink: React.FC<NavLinkProps> = ({ to, children, onClick, className }) => {
  return (
    <Link to={to} onClick={onClick} className={`nav-link-wrapper ${className || ''}`}>
      <div className="nav-link-text-trail">
        <TextTrail 
          text={children}
          fontFamily="Inter, sans-serif"
          fontWeight={600}
          noiseFactor={0.5}
          noiseScale={0.0003}
          rgbPersistFactor={0.99}
          alphaPersistFactor={0.97}
          animateColor={false}
          textColor="#ffffff"
          backgroundColor="#000000"
          supersample={1}
        />
      </div>
    </Link>
  );
};

export default NavLink;
