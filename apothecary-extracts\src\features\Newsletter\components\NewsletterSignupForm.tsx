import React, { useState } from 'react';
import { Input, Button } from '../../../components';
import styles from './NewsletterSignupForm.module.css';

interface NewsletterSignupFormProps {
  onSubmit?: (email: string) => Promise<void> | void;
  className?: string;
  variant?: 'default' | 'inline' | 'card';
  title?: string;
  description?: string;
}

const NewsletterSignupForm: React.FC<NewsletterSignupFormProps> = ({
  onSubmit,
  className,
  variant = 'default',
  title = 'Stay Updated',
  description = 'Get the latest news, deals, and educational content delivered to your inbox.'
}) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('Email address is required');
      return;
    }
    
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    setError('');
    setIsSubmitting(true);
    
    try {
      if (onSubmit) {
        await onSubmit(email);
      } else {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      setIsSuccess(true);
      setEmail('');
    } catch (err) {
      setError('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) setError('');
    if (isSuccess) setIsSuccess(false);
  };

  if (isSuccess) {
    return (
      <div className={`${styles.newsletterForm} ${styles[variant]} ${className || ''}`}>
        <div className={styles.successMessage}>
          <div className={styles.successIcon}>✓</div>
          <h3 className={styles.successTitle}>Thank you for subscribing!</h3>
          <p className={styles.successText}>
            You'll receive our latest updates and exclusive offers.
          </p>
          <Button
            variant="outline"
            size="small"
            onClick={() => setIsSuccess(false)}
          >
            Subscribe Another Email
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${styles.newsletterForm} ${styles[variant]} ${className || ''}`}>
      {(title || description) && (
        <div className={styles.header}>
          {title && <h3 className={styles.title}>{title}</h3>}
          {description && <p className={styles.description}>{description}</p>}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.inputGroup}>
          <Input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={handleEmailChange}
            variant={error ? 'error' : 'default'}
            errorMessage={error}
            fullWidth={variant !== 'inline'}
            size="medium"
            required
            aria-label="Email address for newsletter subscription"
          />
          
          <Button
            type="submit"
            variant="primary"
            size="medium"
            loading={isSubmitting}
            disabled={!email.trim() || isSubmitting}
            className={styles.submitButton}
          >
            {isSubmitting ? 'Subscribing...' : 'Sign Up Today'}
          </Button>
        </div>
      </form>
      
      <div className={styles.disclaimer}>
        <p>
          By subscribing, you agree to receive marketing emails from Apothecary Extracts. 
          You can unsubscribe at any time. View our{' '}
          <a href="/privacy-policy" className={styles.link}>Privacy Policy</a>.
        </p>
      </div>
    </div>
  );
};

export default NewsletterSignupForm;
