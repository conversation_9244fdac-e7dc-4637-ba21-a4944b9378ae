/* CustomerReviewBlock Component Styles */

.reviewBlock {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform var(--transition-fast);
}

.reviewBlock:hover {
  transform: translateY(-2px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-md);
  gap: var(--space-md);
}

.rating {
  display: flex;
  gap: 2px;
  font-size: var(--font-size-lg);
  line-height: 1;
}

.star {
  color: var(--color-accent);
}

.starHalf {
  color: var(--color-accent);
  position: relative;
}

.starHalf::before {
  content: "★";
  position: absolute;
  left: 0;
  width: 50%;
  overflow: hidden;
  color: var(--color-accent);
}

.starEmpty {
  color: var(--color-border);
}

.source {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-align: right;
  flex-shrink: 0;
}

.reviewText {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  line-height: var(--line-height-body);
  margin: 0 0 var(--space-md) 0;
  font-style: italic;
  position: relative;
}

.reviewText::before {
  content: "\201C";
  font-size: 3rem;
  color: var(--color-primary-brand);
  position: absolute;
  top: -10px;
  left: -15px;
  line-height: 1;
  opacity: 0.3;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-md);
  margin-top: auto;
}

.customerName {
  font-size: var(--font-size-sm);
  color: var(--color-primary-brand);
  font-weight: var(--font-weight-semibold);
  font-style: normal;
}

.date {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .source {
    text-align: left;
  }
  
  .footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }
  
  .rating {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .reviewText {
    font-size: var(--font-size-sm);
  }
  
  .reviewText::before {
    font-size: 2rem;
    top: -5px;
    left: -10px;
  }
}
