{"version": 3, "file": "getParserServices.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/getParserServices.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,QAAQ,MAAM,cAAc,CAAC;AAC9C,OAAO,KAAK,EACV,cAAc,EACd,iCAAiC,EAClC,MAAM,cAAc,CAAC;AAWtB;;;GAGG;AACH,wBAAgB,iBAAiB,CAC/B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,GAC3D,iCAAiC,CAAC;AACrC;;;GAGG;AACH,wBAAgB,iBAAiB,CAC/B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAC5D,+BAA+B,EAAE,KAAK,GACrC,iCAAiC,CAAC;AACrC;;;GAGG;AACH,wBAAgB,iBAAiB,CAC/B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAC5D,+BAA+B,EAAE,IAAI,GACpC,cAAc,CAAC;AAClB;;;GAGG;AACH,wBAAgB,iBAAiB,CAC/B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAElC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAC5D,+BAA+B,EAAE,OAAO,GACvC,cAAc,CAAC"}