# Design System Tokens for Apothecary Extracts Website

This document defines the core design tokens and guidelines for the Apothecary Extracts website, ensuring consistency and maintainability across the application. These values are derived from cannabis industry best practices and modern web design standards.

## 1. Colors

### Brand Colors
```css
--color-primary-brand: #2D5016;     /* Deep forest green - main CTAs, prominent text */
--color-secondary-brand: #4A7C59;   /* Medium green - secondary buttons, highlights */
--color-accent: #D4A574;            /* Warm gold - interactive elements, small details */
```

### Neutral Colors
```css
--color-text-primary: #1A1A1A;      /* Almost black - main body text */
--color-text-secondary: #666666;    /* Medium gray - lighter text, subheadings */
--color-background-primary: #FFFFFF; /* White - main page background */
--color-background-secondary: #F8F9FA; /* Light gray - section backgrounds, cards */
--color-border: #E5E5E5;            /* Light gray - input borders, dividers */
```

### Semantic Colors
```css
--color-success: #28A745;           /* Green - form success messages */
--color-warning: #FFC107;           /* Amber - warnings, alerts */
--color-error: #DC3545;             /* Red - form validation errors */
```

## 2. Typography

### Font Families
```css
--font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-family-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-family-monospace: 'Monaco', 'Consolas', monospace;
```

### Font Sizes (Responsive Scale)
```css
--font-size-xs: 0.75rem;    /* 12px */
--font-size-sm: 0.875rem;   /* 14px */
--font-size-base: 1rem;     /* 16px - Base for body text */
--font-size-lg: 1.125rem;   /* 18px */
--font-size-xl: 1.25rem;    /* 20px */
--font-size-h1: 3rem;       /* 48px */
--font-size-h2: 2.25rem;    /* 36px */
--font-size-h3: 1.875rem;   /* 30px */
--font-size-h4: 1.5rem;     /* 24px */
--font-size-h5: 1.25rem;    /* 20px */
--font-size-h6: 1rem;       /* 16px */
```

### Font Weights
```css
--font-weight-light: 300;
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

### Line Heights
```css
--line-height-heading: 1.2;
--line-height-body: 1.6;
```

## 3. Spacing

Consistent spacing scale based on 8px grid system:

```css
--space-xs: 0.25rem;    /* 4px */
--space-sm: 0.5rem;     /* 8px */
--space-md: 1rem;       /* 16px */
--space-lg: 1.5rem;     /* 24px */
--space-xl: 2rem;       /* 32px */
--space-xxl: 3rem;      /* 48px */
--space-xxxl: 4rem;     /* 64px */
```

## 4. Breakpoints

Responsive breakpoints for layout adaptation:

```css
--breakpoint-sm: 640px;   /* Small tablets and large phones (portrait) */
--breakpoint-md: 768px;   /* Tablets (landscape) */
--breakpoint-lg: 1024px;  /* Small laptops and desktops */
--breakpoint-xl: 1280px;  /* Large desktops */
```

## 5. Border Radius

Standard border-radius values for consistent rounded corners:

```css
--border-radius-sm: 4px;
--border-radius-md: 8px;
--border-radius-lg: 12px;
--border-radius-full: 50%;  /* For circular elements */
```

## 6. Z-Index

Consistent layering order for overlapping elements:

```css
--z-index-base: 1;
--z-index-dropdown: 10;
--z-index-sticky: 100;
--z-index-modal: 1000;
--z-index-overlay: 1001;
```

## 7. Shadow

Standard box-shadow values for consistent depth effects:

```css
--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
--shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
```

## 8. Component Guidelines

### Buttons
- Primary: Use `--color-primary-brand` background with white text
- Secondary: Use `--color-secondary-brand` background with white text
- Accent: Use `--color-accent` background with dark text
- Hover states: Darken background by 10%

### Cards
- Background: `--color-background-secondary`
- Border radius: `--border-radius-md`
- Shadow: `--shadow-sm`
- Padding: `--space-lg`

### Forms
- Input border: `--color-border`
- Focus border: `--color-primary-brand`
- Error border: `--color-error`
- Success border: `--color-success`

### Navigation
- Background: `--color-background-primary`
- Text: `--color-text-primary`
- Active/hover: `--color-primary-brand`
- Border: `--color-border`

## 9. Accessibility Guidelines

- Minimum contrast ratio: 4.5:1 for normal text, 3:1 for large text
- Focus indicators: 2px solid `--color-primary-brand`
- Touch targets: Minimum 44px × 44px
- Font size: Minimum 16px for body text
