/* ProductCategoryCard Component Styles */

.categoryCard {
  height: 100%;
  transition: all var(--transition-fast);
  border: 2px solid transparent;
}

.categoryCard:hover {
  border-color: var(--color-primary-brand);
  transform: translateY(-4px);
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
  position: relative;
}

.imageContainer {
  width: 100%;
  height: 120px;
  margin-bottom: var(--space-md);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background-color: var(--color-background-secondary);
}

.categoryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform var(--transition-fast);
}

.categoryCard:hover .categoryImage {
  transform: scale(1.05);
}

.iconContainer {
  width: 80px;
  height: 80px;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  color: var(--color-primary-brand);
  font-size: 2rem;
}

.textContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.categoryName {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary-brand);
  margin-bottom: var(--space-sm);
  transition: color var(--transition-fast);
}

.categoryCard:hover .categoryName {
  color: var(--color-secondary-brand);
}

.categoryDescription {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-sm);
  line-height: var(--line-height-body);
}

.productCount {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.arrow {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  color: var(--color-text-secondary);
  opacity: 0;
  transform: translateX(-10px);
  transition: all var(--transition-fast);
}

.categoryCard:hover .arrow {
  opacity: 1;
  transform: translateX(0);
  color: var(--color-primary-brand);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .imageContainer {
    height: 100px;
  }
  
  .iconContainer {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .categoryName {
    font-size: var(--font-size-h6);
  }
  
  .categoryDescription {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  .arrow {
    display: none; /* Hide arrow on very small screens */
  }
  
  .imageContainer {
    height: 80px;
  }
  
  .iconContainer {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}
