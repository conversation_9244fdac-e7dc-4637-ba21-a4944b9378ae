/* LocationCard Component Styles */

.locationCard {
  transition: transform var(--transition-fast);
}

.locationCard:hover {
  transform: translateY(-4px);
}

.imageContainer {
  margin-bottom: var(--space-lg);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.locationImage {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.content {
  text-align: center;
}

.locationName {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary-brand);
  margin-bottom: var(--space-md);
}

.addressSection {
  margin-bottom: var(--space-lg);
}

.address {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  line-height: var(--line-height-body);
}

.phone {
  margin: 0;
}

.phoneLink {
  color: var(--color-primary-brand);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.phoneLink:hover,
.phoneLink:focus {
  color: var(--color-secondary-brand);
  text-decoration: underline;
}

.hoursSection {
  margin-bottom: var(--space-lg);
  padding: var(--space-md);
  background-color: var(--color-background-secondary);
  border-radius: var(--border-radius-md);
}

.hours {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  margin: 0;
}

.shopButtons {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;
}

.shopButton {
  min-width: 140px;
}

.actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.directionsButton,
.callButton {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 36px;
}

.directionsButton:hover,
.directionsButton:focus,
.callButton:hover,
.callButton:focus {
  border-color: var(--color-primary-brand);
  color: var(--color-primary-brand);
  background-color: rgba(45, 80, 22, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shopButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .shopButton {
    width: 100%;
    max-width: 200px;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .directionsButton,
  .callButton {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .locationName {
    font-size: var(--font-size-h5);
  }
  
  .imageContainer {
    margin-bottom: var(--space-md);
  }
  
  .locationImage {
    height: 150px;
  }
}
