import React, { useState } from 'react';
import styles from './Accordion.module.css';

interface AccordionItemProps {
  title: string;
  children: React.ReactNode;
  isOpen?: boolean;
  onToggle?: () => void;
}

interface AccordionProps {
  children: React.ReactElement<AccordionItemProps>[];
  allowMultiple?: boolean;
  className?: string;
}

export const AccordionItem: React.FC<AccordionItemProps> = ({
  title,
  children,
  isOpen = false,
  onToggle
}) => {
  const itemId = `accordion-item-${Math.random().toString(36).substr(2, 9)}`;
  const contentId = `${itemId}-content`;
  const headerId = `${itemId}-header`;

  return (
    <div className={styles.accordionItem}>
      <button
        id={headerId}
        className={styles.accordionHeader}
        onClick={onToggle}
        aria-expanded={isOpen}
        aria-controls={contentId}
      >
        <span className={styles.accordionTitle}>{title}</span>
        <span className={`${styles.accordionIcon} ${isOpen ? styles.accordionIconOpen : ''}`}>
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
          >
            <path
              d="M4 6L8 10L12 6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </span>
      </button>
      
      <div
        id={contentId}
        className={`${styles.accordionContent} ${isOpen ? styles.accordionContentOpen : ''}`}
        role="region"
        aria-labelledby={headerId}
        hidden={!isOpen}
      >
        <div className={styles.accordionContentInner}>
          {children}
        </div>
      </div>
    </div>
  );
};

const Accordion: React.FC<AccordionProps> = ({
  children,
  allowMultiple = false,
  className
}) => {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const handleToggle = (index: number) => {
    setOpenItems(prev => {
      const newOpenItems = new Set(prev);
      
      if (newOpenItems.has(index)) {
        newOpenItems.delete(index);
      } else {
        if (!allowMultiple) {
          newOpenItems.clear();
        }
        newOpenItems.add(index);
      }
      
      return newOpenItems;
    });
  };

  return (
    <div className={`${styles.accordion} ${className || ''}`}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            isOpen: openItems.has(index),
            onToggle: () => handleToggle(index)
          });
        }
        return child;
      })}
    </div>
  );
};

export default Accordion;
