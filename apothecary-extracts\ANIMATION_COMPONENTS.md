# React Text Animation Components Integration

This document outlines the three React text animation components that have been integrated into the Apothecary Extracts website.

## Components Implemented

### 1. CurvedLoop Component
**Location**: `src/components/CurvedLoop/`
**Usage**: Hero section with "APOTHECARY EXTRACTS" text
**Features**:
- Curved text animation along an SVG path
- Interactive dragging to control direction
- Customizable speed, curve amount, and direction
- Responsive design

**Implementation**:
```tsx
<CurvedLoop 
  marqueeText="APOTHECARY EXTRACTS"
  speed={2}
  curveAmount={300}
  direction="left"
  interactive={true}
  className={styles.curvedLoopText}
/>
```

### 2. TextTrail Component
**Location**: `src/components/TextTrail/`
**Usage**: Navigation text effects
**Features**:
- Three.js-based fluid text animation
- Mouse interaction creates trailing effects
- Customizable colors, noise, and persistence factors
- WebGL shader-based rendering

**Implementation**:
```tsx
<TextTrail 
  text="Navigation Text"
  fontFamily="Inter, sans-serif"
  fontWeight={600}
  noiseFactor={0.5}
  noiseScale={0.0003}
  rgbPersistFactor={0.99}
  alphaPersistFactor={0.97}
  animateColor={false}
  textColor="#ffffff"
  backgroundColor="#000000"
  supersample={1}
/>
```

### 3. ASCIIText Component
**Location**: `src/components/ASCIIText/`
**Usage**: Header logo with "AE" letters
**Features**:
- Three.js-based ASCII art rendering
- Real-time text-to-ASCII conversion
- Wave animation effects
- Customizable font sizes and colors

**Implementation**:
```tsx
<ASCIIText 
  text="AE" 
  asciiFontSize={6}
  textFontSize={120}
  textColor="#ffffff"
  planeBaseHeight={4}
  enableWaves={true}
/>
```

## Integration Points

### Header (Logo)
- **File**: `src/layout/Header/Header.tsx`
- **Component**: ASCIIText with "AE" text
- **Styling**: `src/layout/Header/Header.module.css` (`.logoContainer`)

### Navigation Links
- **File**: `src/components/NavLink/NavLink.tsx`
- **Component**: TextTrail for all navigation text
- **Usage**: Both desktop and mobile navigation menus

### Homepage Hero
- **File**: `src/pages/HomePage.tsx`
- **Component**: CurvedLoop with "APOTHECARY EXTRACTS"
- **Styling**: `src/pages/HomePage.module.css` (`.heroTitle`, `.curvedLoopText`)

## Dependencies Added

```json
{
  "three": "^0.x.x",
  "@types/three": "^0.x.x"
}
```

## File Structure

```
src/
├── components/
│   ├── ASCIIText/
│   │   ├── ASCIIText.tsx
│   │   └── index.ts
│   ├── CurvedLoop/
│   │   ├── CurvedLoop.tsx
│   │   ├── CurvedLoop.css
│   │   └── index.ts
│   ├── TextTrail/
│   │   ├── TextTrail.tsx
│   │   ├── TextTrail.css
│   │   └── index.ts
│   └── NavLink/
│       ├── NavLink.tsx
│       ├── NavLink.css
│       └── index.ts
```

## Performance Considerations

1. **Three.js Components**: ASCIIText and TextTrail use WebGL rendering
2. **Responsive Design**: All components adapt to different screen sizes
3. **Lazy Loading**: Components initialize only when visible
4. **Memory Management**: Proper cleanup in useEffect hooks

## Customization Options

### CurvedLoop
- `marqueeText`: Text to display
- `speed`: Animation speed (default: 2)
- `curveAmount`: Curve intensity (default: 400)
- `direction`: "left" or "right"
- `interactive`: Enable/disable mouse interaction

### TextTrail
- `text`: Text to display
- `fontFamily`: Font family
- `fontWeight`: Font weight
- `noiseFactor`: Noise intensity
- `textColor`: Text color
- `backgroundColor`: Background color

### ASCIIText
- `text`: Text to convert to ASCII
- `asciiFontSize`: ASCII character size
- `textFontSize`: Original text size
- `textColor`: Text color
- `enableWaves`: Enable wave animation

## Browser Compatibility

- **Modern Browsers**: Full support (Chrome, Firefox, Safari, Edge)
- **WebGL Required**: For TextTrail and ASCIIText components
- **Fallback**: Consider adding fallbacks for older browsers if needed

## Development Server

Run the development server:
```bash
npm run dev
```

The server will start at `http://localhost:5173/`
