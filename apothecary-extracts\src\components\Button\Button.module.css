/* Button Component Styles */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-body);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
}

.button:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Variants */
.primary {
  background-color: var(--color-primary-brand);
  color: white;
}

.primary:hover:not(:disabled) {
  background-color: #1e3a0f;
  transform: translateY(-1px);
}

.primary:active:not(:disabled) {
  transform: translateY(0);
}

.secondary {
  background-color: var(--color-secondary-brand);
  color: white;
}

.secondary:hover:not(:disabled) {
  background-color: #3a6b47;
  transform: translateY(-1px);
}

.secondary:active:not(:disabled) {
  transform: translateY(0);
}

.accent {
  background-color: var(--color-accent);
  color: var(--color-text-primary);
}

.accent:hover:not(:disabled) {
  background-color: #c19660;
  transform: translateY(-1px);
}

.accent:active:not(:disabled) {
  transform: translateY(0);
}

.outline {
  background-color: transparent;
  color: var(--color-primary-brand);
  border: 2px solid var(--color-primary-brand);
}

.outline:hover:not(:disabled) {
  background-color: var(--color-primary-brand);
  color: white;
}

.outline:active:not(:disabled) {
  transform: translateY(0);
}

/* Sizes */
.small {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.medium {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-base);
  min-height: 44px;
}

.large {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Modifiers */
.fullWidth {
  width: 100%;
}

.loading {
  pointer-events: none;
}

.loadingText {
  opacity: 0.7;
}

/* Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .button {
    min-height: 44px; /* Ensure touch target size on mobile */
  }
  
  .small {
    min-height: 40px;
  }
  
  .large {
    min-height: 48px;
  }
}
