/* Age Gate Modal Styles */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--space-md);
}

.modal {
  background: var(--color-background-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.content {
  padding: var(--space-xl);
  text-align: center;
}

.logo {
  margin-bottom: var(--space-lg);
}

.brandName {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-brand);
  margin: 0;
}

.title {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.message {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-body);
}

.disclaimer {
  background-color: var(--color-background-secondary);
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--space-lg);
  border-left: 4px solid var(--color-accent);
}

.disclaimer p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-body);
}

.buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.confirmButton {
  background-color: var(--color-primary-brand);
  color: white;
  border: none;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  min-height: 44px;
}

.confirmButton:hover {
  background-color: #1e3a0f;
}

.confirmButton:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

.denyButton {
  background-color: transparent;
  color: var(--color-text-secondary);
  border: 2px solid var(--color-border);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px;
}

.denyButton:hover {
  border-color: var(--color-text-secondary);
  color: var(--color-text-primary);
}

.denyButton:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

.legalNotice {
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-md);
}

.legalNotice p {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-body);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .buttons {
    flex-direction: row;
    justify-content: center;
  }
  
  .confirmButton,
  .denyButton {
    flex: 1;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .overlay {
    padding: var(--space-sm);
  }
  
  .content {
    padding: var(--space-lg);
  }
  
  .brandName {
    font-size: var(--font-size-h4);
  }
  
  .title {
    font-size: var(--font-size-h5);
  }
  
  .message {
    font-size: var(--font-size-base);
  }
}
