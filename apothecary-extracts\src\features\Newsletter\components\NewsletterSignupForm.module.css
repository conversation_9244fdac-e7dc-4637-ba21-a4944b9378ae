/* NewsletterSignupForm Component Styles */

.newsletterForm {
  width: 100%;
}

/* Variants */
.default {
  text-align: center;
}

.inline {
  text-align: left;
}

.card {
  background-color: var(--color-background-secondary);
  padding: var(--space-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
}

/* Header */
.header {
  margin-bottom: var(--space-lg);
}

.title {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-body);
  margin: 0;
}

/* Form */
.form {
  margin-bottom: var(--space-md);
}

.inputGroup {
  display: flex;
  gap: var(--space-md);
  align-items: flex-start;
  max-width: 500px;
  margin: 0 auto;
}

.inline .inputGroup {
  margin: 0;
}

.submitButton {
  flex-shrink: 0;
  min-width: 140px;
}

/* Success state */
.successMessage {
  text-align: center;
  padding: var(--space-xl);
}

.successIcon {
  width: 60px;
  height: 60px;
  background-color: var(--color-success);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0 auto var(--space-lg);
}

.successTitle {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.successText {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-body);
}

/* Disclaimer */
.disclaimer {
  text-align: center;
}

.disclaimer p {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-body);
  margin: 0;
}

.link {
  color: var(--color-primary-brand);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.link:hover,
.link:focus {
  color: var(--color-secondary-brand);
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .inputGroup {
    flex-direction: column;
    align-items: stretch;
  }
  
  .submitButton {
    min-width: auto;
    width: 100%;
  }
  
  .card {
    padding: var(--space-lg);
  }
  
  .title {
    font-size: var(--font-size-h5);
  }
}

@media (max-width: 480px) {
  .successIcon {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-lg);
  }
  
  .successTitle {
    font-size: var(--font-size-h6);
  }
  
  .description,
  .successText {
    font-size: var(--font-size-sm);
  }
}
