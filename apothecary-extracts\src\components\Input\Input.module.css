/* Input Component Styles */

.inputWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.fullWidth {
  width: 100%;
}

.label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.required {
  color: var(--color-error);
  font-weight: var(--font-weight-bold);
}

.input {
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  transition: all var(--transition-fast);
  width: 100%;
}

.input:focus {
  outline: none;
  border-color: var(--color-primary-brand);
  box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
}

.input:disabled {
  background-color: var(--color-background-secondary);
  color: var(--color-text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

.input::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

/* Variants */
.default {
  border-color: var(--color-border);
}

.error {
  border-color: var(--color-error);
}

.error:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.success {
  border-color: var(--color-success);
}

.success:focus {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Sizes */
.small {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.medium {
  padding: var(--space-md);
  font-size: var(--font-size-base);
  min-height: 44px;
}

.large {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Helper and error text */
.helperText {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-body);
}

.errorMessage {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-body);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .input {
    min-height: 44px; /* Ensure touch target size on mobile */
  }
  
  .small {
    min-height: 40px;
  }
  
  .large {
    min-height: 48px;
  }
}
