/* Main Layout Styles */

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layoutContent {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: filter var(--transition-normal);
}

.layoutBlocked {
  filter: blur(3px);
  pointer-events: none;
  user-select: none;
}

.skipLink {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary-brand);
  color: white;
  padding: var(--space-sm) var(--space-md);
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  z-index: var(--z-index-overlay);
  transition: top var(--transition-fast);
  font-weight: var(--font-weight-medium);
}

.skipLink:focus {
  top: 6px;
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}
