import * as ts from 'typescript';
/**
 * Returns the contextual type of a given node.
 * Contextual type is the type of the target the node is going into.
 * i.e. the type of a called function's parameter, or the defined type of a variable declaration
 */
export declare function getContextualType(checker: ts.<PERSON><PERSON><PERSON><PERSON>, node: ts.Expression): ts.Type | undefined;
//# sourceMappingURL=getContextualType.d.ts.map