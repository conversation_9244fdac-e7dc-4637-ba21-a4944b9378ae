/* Header Styles */

.header {
  background-color: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-sm);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

/* Logo */
.logo {
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo:hover {
  text-decoration: none;
}

.brandName {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-brand);
  margin: 0;
  line-height: 1;
}

.tagline {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-top: 2px;
  font-weight: var(--font-weight-normal);
}

/* Desktop Navigation */
.desktopNav {
  display: none;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-xl);
}

.navLink {
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  padding: var(--space-sm) 0;
  transition: color var(--transition-fast);
  position: relative;
}

.navLink:hover,
.navLink:focus {
  color: var(--color-primary-brand);
  text-decoration: none;
}

.navLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary-brand);
  transition: width var(--transition-fast);
}

.navLink:hover::after,
.navLink:focus::after {
  width: 100%;
}

/* Mobile Menu Button */
.menuButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
  color: var(--color-text-primary);
}

.menuIcon {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 24px;
  height: 18px;
}

.menuLine {
  width: 100%;
  height: 2px;
  background-color: var(--color-text-primary);
  transition: all var(--transition-fast);
  transform-origin: center;
}

.menuLine:nth-child(1).menuLineOpen {
  transform: translateY(8px) rotate(45deg);
}

.menuLine:nth-child(2).menuLineOpen {
  opacity: 0;
}

.menuLine:nth-child(3).menuLineOpen {
  transform: translateY(-8px) rotate(-45deg);
}

.menuText {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* Mobile Navigation */
.mobileNav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: var(--z-index-dropdown);
}

.mobileNavOpen {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobileNavList {
  list-style: none;
  margin: 0;
  padding: var(--space-md);
}

.mobileNavLink {
  display: block;
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
  padding: var(--space-md) 0;
  border-bottom: 1px solid var(--color-border);
  transition: color var(--transition-fast);
}

.mobileNavLink:last-child {
  border-bottom: none;
}

.mobileNavLink:hover,
.mobileNavLink:focus {
  color: var(--color-primary-brand);
  text-decoration: none;
}

.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-base);
}

/* Desktop styles */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-lg);
  }
  
  .desktopNav {
    display: block;
  }
  
  .menuButton {
    display: none;
  }
  
  .mobileNav {
    display: none;
  }
  
  .brandName {
    font-size: var(--font-size-h4);
  }
  
  .tagline {
    font-size: var(--font-size-sm);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-xl);
  }
  
  .headerContent {
    height: 90px;
  }
  
  .navList {
    gap: var(--space-xxl);
  }
}
