import React from 'react';
import { Button, Accordion, AccordionItem } from '../components';
import LocationCard from '../features/Locations/components/LocationCard';
import ProductCategoryCard from '../features/Shop/components/ProductCategoryCard';
import CustomerReviewBlock from '../features/Locations/components/CustomerReviewBlock';
import NewsletterSignupForm from '../features/Newsletter/components/NewsletterSignupForm';
import styles from './HomePage.module.css';

const HomePage: React.FC = () => {
  return (
    <div className={styles.homePage}>
      {/* Hero Section */}
      <section className={styles.hero}>
        <div className={styles.container}>
          <div className={styles.heroContent}>
            <h1 className={styles.heroTitle}>
              Premium Cannabis Extracts & Products
            </h1>
            <p className={styles.heroSubtitle}>
              Craft-quality cannabis products where cutting-edge technology 
              meets passionate cultivation. Serving Massachusetts with excellence.
            </p>
            <div className={styles.heroButtons}>
              <Button variant="accent" size="large">Shop Now</Button>
              <Button variant="outline" size="large">View Featured</Button>
            </div>
          </div>
        </div>
      </section>

      {/* Store Finder Section */}
      <section className={styles.storeSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Our Dispensary Locations</h2>
          <div className={styles.storeGrid}>
            <LocationCard
              name="Hadley Dispensary"
              address="123 Main Street"
              city="Hadley"
              state="MA"
              zipCode="01035"
              phone="(*************"
              hours="Mon-Sun: 9AM-9PM"
              hasMedical={true}
              hasRecreational={true}
              onShopMedical={() => console.log('Shop Medical - Hadley')}
              onShopRecreational={() => console.log('Shop Recreational - Hadley')}
            />
            <LocationCard
              name="Bernardston Dispensary"
              address="456 Route 5"
              city="Bernardston"
              state="MA"
              zipCode="01337"
              phone="(*************"
              hours="Mon-Sun: 10AM-8PM"
              hasMedical={false}
              hasRecreational={true}
              onShopRecreational={() => console.log('Shop Recreational - Bernardston')}
            />
          </div>
        </div>
      </section>

      {/* Product Categories Section */}
      <section className={styles.categoriesSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Shop by Category</h2>
          <div className={styles.categoriesGrid}>
            <ProductCategoryCard
              name="Flower"
              description="Premium cannabis flower"
              productCount={45}
              onClick={() => console.log('Navigate to Flower')}
            />
            <ProductCategoryCard
              name="Pre-rolls"
              description="Ready-to-smoke joints"
              productCount={28}
              onClick={() => console.log('Navigate to Pre-rolls')}
            />
            <ProductCategoryCard
              name="Edibles"
              description="Cannabis-infused treats"
              productCount={32}
              onClick={() => console.log('Navigate to Edibles')}
            />
            <ProductCategoryCard
              name="Extracts"
              description="Concentrated cannabis products"
              productCount={18}
              onClick={() => console.log('Navigate to Extracts')}
            />
            <ProductCategoryCard
              name="Tinctures"
              description="Liquid cannabis extracts"
              productCount={12}
              onClick={() => console.log('Navigate to Tinctures')}
            />
            <ProductCategoryCard
              name="Vapes"
              description="Vaporizer cartridges"
              productCount={24}
              onClick={() => console.log('Navigate to Vapes')}
            />
          </div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className={styles.brandSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Where Technology Meets Passion in Craft Cannabis</h2>
          <p className={styles.brandDescription}>
            At Apothecary Extracts, we combine cutting-edge extraction technology 
            with passionate cultivation to create the finest cannabis products in Massachusetts. 
            Our vertically integrated approach ensures quality from seed to sale.
          </p>
          <div className={styles.brandButtons}>
            <Button variant="outline" size="medium">Our Cultivation</Button>
            <Button variant="outline" size="medium">View Strains</Button>
          </div>
        </div>
      </section>

      {/* Customer Testimonials Section */}
      <section className={styles.testimonialsSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>What Our Customers Say</h2>
          <div className={styles.testimonialsGrid}>
            <CustomerReviewBlock
              customerName="Sarah M."
              reviewText="Amazing quality products and incredibly knowledgeable staff. They helped me find exactly what I needed for my medical condition."
              rating={5}
              source="Google Review"
              location="Hadley Location"
              date="2024-01-15"
            />
            <CustomerReviewBlock
              customerName="Mike R."
              reviewText="Best dispensary in Western Mass! Great selection, fair prices, and the staff always makes me feel welcome."
              rating={5}
              source="Google Review"
              location="Bernardston Location"
              date="2024-01-10"
            />
            <CustomerReviewBlock
              customerName="Jennifer L."
              reviewText="First time visiting and the team was so patient and helpful. They explained everything and made sure I was comfortable."
              rating={5}
              source="Google Review"
              location="Hadley Location"
              date="2024-01-08"
            />
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={styles.faqSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Visiting Our MA Dispensaries For The First Time?</h2>
          <div className={styles.faqContainer}>
            <Accordion allowMultiple={false}>
              <AccordionItem title="What do I need to bring to purchase cannabis?">
                <p>For recreational purchases, you must be 21 or older and bring a valid government-issued photo ID. For medical purchases, you need a valid Massachusetts Medical Marijuana ID card and a government-issued photo ID.</p>
              </AccordionItem>
              <AccordionItem title="How much can I purchase?">
                <p>Recreational customers can purchase up to 1 ounce of flower or its equivalent in other products per day. Medical patients can purchase up to a 60-day supply as determined by their physician.</p>
              </AccordionItem>
              <AccordionItem title="Do you help first-time customers?">
                <p>Absolutely! Our knowledgeable staff is here to help guide you through your first cannabis experience. We'll explain different products, dosing, and help you find what's right for your needs.</p>
              </AccordionItem>
              <AccordionItem title="What are your hours?">
                <p>Our Hadley location is open Monday-Sunday 9AM-9PM. Our Bernardston location is open Monday-Sunday 10AM-8PM. Hours may vary on holidays.</p>
              </AccordionItem>
              <AccordionItem title="Do you offer both medical and recreational products?">
                <p>Our Hadley location serves both medical and recreational customers. Our Bernardston location currently serves recreational customers only.</p>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className={styles.newsletterSection}>
        <div className={styles.container}>
          <NewsletterSignupForm
            title="Stay Updated"
            description="Get the latest news, deals, and educational content delivered to your inbox."
            onSubmit={async (email) => {
              console.log('Newsletter signup:', email);
              // Here you would typically send the email to your backend
            }}
          />
        </div>
      </section>
    </div>
  );
};

export default HomePage;
