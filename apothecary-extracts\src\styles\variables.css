/* Design System Variables for Apothecary Extracts */

:root {
  /* Brand Colors */
  --color-primary-brand: #2D5016;
  --color-secondary-brand: #4A7C59;
  --color-accent: #D4A574;

  /* Neutral Colors */
  --color-text-primary: #1A1A1A;
  --color-text-secondary: #666666;
  --color-background-primary: #FFFFFF;
  --color-background-secondary: #F8F9FA;
  --color-border: #E5E5E5;

  /* Semantic Colors */
  --color-success: #28A745;
  --color-warning: #FFC107;
  --color-error: #DC3545;

  /* Typography */
  --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-monospace: 'Monaco', 'Consolas', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-h1: 3rem;       /* 48px */
  --font-size-h2: 2.25rem;    /* 36px */
  --font-size-h3: 1.875rem;   /* 30px */
  --font-size-h4: 1.5rem;     /* 24px */
  --font-size-h5: 1.25rem;    /* 20px */
  --font-size-h6: 1rem;       /* 16px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-heading: 1.2;
  --line-height-body: 1.6;

  /* Spacing */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-xxl: 3rem;      /* 48px */
  --space-xxxl: 4rem;     /* 64px */

  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;

  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-full: 50%;

  /* Z-Index */
  --z-index-base: 1;
  --z-index-dropdown: 10;
  --z-index-sticky: 100;
  --z-index-modal: 1000;
  --z-index-overlay: 1001;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Responsive font sizes */
@media (max-width: 768px) {
  :root {
    --font-size-h1: 2.5rem;    /* 40px */
    --font-size-h2: 2rem;      /* 32px */
    --font-size-h3: 1.5rem;    /* 24px */
    --font-size-h4: 1.25rem;   /* 20px */
  }
}
