# Apothecary Extracts - Cannabis Dispensary Website

A modern, responsive website for Apothecary Extracts, a premium cannabis dispensary serving Massachusetts. Built with React, TypeScript, and Vite following industry best practices for accessibility, performance, and legal compliance.

## 🌿 Features

### Legal Compliance
- **Age Verification Modal**: Mandatory age gate requiring users to confirm they are 21+ or qualified medical patients
- **Location-Based Services**: Separate medical and recreational product access
- **Compliance Messaging**: Appropriate disclaimers and legal notices

### User Experience
- **Responsive Design**: Mobile-first approach with seamless experience across all devices
- **Accessibility**: WCAG AA compliant with proper focus management, ARIA labels, and keyboard navigation
- **Modern UI Components**: Reusable component library with consistent design system

### Core Functionality
- **Dispensary Locations**: Hadley and Bernardston locations with detailed information
- **Product Categories**: Flower, Pre-rolls, Edibles, Extracts, Tinctures, and Vapes
- **Customer Testimonials**: Social proof with Google reviews
- **Educational Content**: FAQ section for first-time customers
- **Newsletter Signup**: Email subscription with validation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd apothecary-extracts
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

```js
export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...

      // Remove tseslint.configs.recommended and replace with this
      ...tseslint.configs.recommendedTypeChecked,
      // Alternatively, use this for stricter rules
      ...tseslint.configs.strictTypeChecked,
      // Optionally, add this for stylistic rules
      ...tseslint.configs.stylisticTypeChecked,

      // Other configs...
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...
      // Enable lint rules for React
      reactX.configs['recommended-typescript'],
      // Enable lint rules for React DOM
      reactDom.configs.recommended,
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```
