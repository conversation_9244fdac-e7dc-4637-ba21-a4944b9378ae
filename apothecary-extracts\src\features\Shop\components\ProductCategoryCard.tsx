import React from 'react';
import { Card } from '../../../components';
import styles from './ProductCategoryCard.module.css';

interface ProductCategoryCardProps {
  name: string;
  description: string;
  image?: string;
  icon?: React.ReactNode;
  productCount?: number;
  onClick?: () => void;
  href?: string;
  className?: string;
}

const ProductCategoryCard: React.FC<ProductCategoryCardProps> = ({
  name,
  description,
  image,
  icon,
  productCount,
  onClick,
  href,
  className
}) => {
  const handleClick = () => {
    if (href) {
      window.location.href = href;
    } else if (onClick) {
      onClick();
    }
  };

  return (
    <Card 
      variant="default" 
      padding="large" 
      clickable={!!(onClick || href)}
      onClick={handleClick}
      className={`${styles.categoryCard} ${className || ''}`}
    >
      <div className={styles.content}>
        {image && (
          <div className={styles.imageContainer}>
            <img 
              src={image} 
              alt={`${name} products`}
              className={styles.categoryImage}
            />
          </div>
        )}
        
        {icon && !image && (
          <div className={styles.iconContainer}>
            {icon}
          </div>
        )}
        
        <div className={styles.textContent}>
          <h3 className={styles.categoryName}>{name}</h3>
          <p className={styles.categoryDescription}>{description}</p>
          
          {productCount !== undefined && (
            <p className={styles.productCount}>
              {productCount} {productCount === 1 ? 'product' : 'products'} available
            </p>
          )}
        </div>
        
        <div className={styles.arrow}>
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
          >
            <path
              d="M7.5 15L12.5 10L7.5 5"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </Card>
  );
};

export default ProductCategoryCard;
