import React, { useState, useEffect } from 'react';
import styles from './AgeGateModal.module.css';

interface AgeGateModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  onDeny: () => void;
}

const AgeGateModal: React.FC<AgeGateModalProps> = ({ isOpen, onConfirm, onDeny }) => {
  const [isVisible, setIsVisible] = useState(isOpen);

  useEffect(() => {
    setIsVisible(isOpen);
  }, [isOpen]);

  useEffect(() => {
    // Prevent scrolling when modal is open
    if (isVisible) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className={styles.overlay} role="dialog" aria-modal="true" aria-labelledby="age-gate-title">
      <div className={styles.modal}>
        <div className={styles.content}>
          <div className={styles.logo}>
            <h1 className={styles.brandName}>Apothecary Extracts</h1>
          </div>
          
          <h2 id="age-gate-title" className={styles.title}>
            Age Verification Required
          </h2>
          
          <p className={styles.message}>
            You must be at least 21 years old or a qualified medical marijuana patient 
            to access this website. Please confirm your age to continue.
          </p>
          
          <div className={styles.disclaimer}>
            <p>
              This website contains information about cannabis products. 
              Cannabis is for use only by adults 21 years of age or older, 
              or qualified medical marijuana patients.
            </p>
          </div>
          
          <div className={styles.buttons}>
            <button 
              className={styles.confirmButton}
              onClick={onConfirm}
              autoFocus
            >
              Yes, I am 21 or older / Qualified Patient
            </button>
            
            <button 
              className={styles.denyButton}
              onClick={onDeny}
            >
              No, I am under 21
            </button>
          </div>
          
          <div className={styles.legalNotice}>
            <p>
              By entering this site, you acknowledge that you are of legal age 
              and agree to our Terms of Use and Privacy Policy.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgeGateModal;
