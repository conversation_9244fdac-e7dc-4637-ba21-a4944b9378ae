import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from './Header/Header';
import Footer from './Footer/Footer';
import AgeGateModal from '../features/Auth/components/AgeGateModal';
import { useAgeVerification } from '../features/Auth/hooks/useAgeVerification';
import styles from './MainLayout.module.css';

const MainLayout: React.FC = () => {
  const { isVerified, showModal, verifyAge, denyAge } = useAgeVerification();

  return (
    <div className={styles.layout}>
      {/* Skip to main content link for accessibility */}
      <a href="#main-content" className={styles.skipLink}>
        Skip to main content
      </a>

      {/* Age Verification Modal */}
      <AgeGateModal 
        isOpen={showModal}
        onConfirm={verifyAge}
        onDeny={denyAge}
      />

      {/* Main Layout Structure */}
      <div className={`${styles.layoutContent} ${!isVerified ? styles.layoutBlocked : ''}`}>
        <Header />
        
        <main id="main-content" className={styles.main}>
          <Outlet />
        </main>
        
        <Footer />
      </div>
    </div>
  );
};

export default MainLayout;
