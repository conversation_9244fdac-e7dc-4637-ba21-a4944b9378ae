import React from 'react';
import { <PERSON>, Button } from '../../../components';
import styles from './LocationCard.module.css';

interface LocationCardProps {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone?: string;
  hours?: string;
  hasMedical?: boolean;
  hasRecreational?: boolean;
  image?: string;
  onShopMedical?: () => void;
  onShopRecreational?: () => void;
  className?: string;
}

const LocationCard: React.FC<LocationCardProps> = ({
  name,
  address,
  city,
  state,
  zipCode,
  phone,
  hours,
  hasMedical = false,
  hasRecreational = false,
  image,
  onShopMedical,
  onShopRecreational,
  className
}) => {
  const fullAddress = `${address}, ${city}, ${state} ${zipCode}`;

  return (
    <Card variant="elevated" padding="large" className={`${styles.locationCard} ${className || ''}`}>
      {image && (
        <div className={styles.imageContainer}>
          <img 
            src={image} 
            alt={`${name} exterior`}
            className={styles.locationImage}
          />
        </div>
      )}
      
      <div className={styles.content}>
        <h3 className={styles.locationName}>{name}</h3>
        
        <div className={styles.addressSection}>
          <p className={styles.address}>{fullAddress}</p>
          {phone && (
            <p className={styles.phone}>
              <a href={`tel:${phone}`} className={styles.phoneLink}>
                {phone}
              </a>
            </p>
          )}
        </div>
        
        {hours && (
          <div className={styles.hoursSection}>
            <p className={styles.hours}>
              <strong>Hours:</strong> {hours}
            </p>
          </div>
        )}
        
        <div className={styles.shopButtons}>
          {hasMedical && (
            <Button
              variant="primary"
              size="medium"
              onClick={onShopMedical}
              className={styles.shopButton}
            >
              Shop Medical
            </Button>
          )}
          
          {hasRecreational && (
            <Button
              variant="secondary"
              size="medium"
              onClick={onShopRecreational}
              className={styles.shopButton}
            >
              Shop Recreational
            </Button>
          )}
        </div>
        
        <div className={styles.actions}>
          <button className={styles.directionsButton}>
            Get Directions
          </button>
          <button className={styles.callButton}>
            Call Store
          </button>
        </div>
      </div>
    </Card>
  );
};

export default LocationCard;
