import { useEffect, useRef } from "react";
import {
  CanvasTexture,
  Clock,
  Color,
  LinearFilter,
  LinearMipmapLinearFilter,
  Mesh,
  OrthographicCamera,
  PlaneGeometry,
  Scene,
  ShaderMaterial,
  Vector2,
  Vector3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  WebGLRenderTarget,
} from "three";
import "./TextTrail.css";

interface TextTrailProps {
  text?: string;
  fontFamily?: string;
  fontWeight?: string | number;
  noiseFactor?: number;
  noiseScale?: number;
  rgbPersistFactor?: number;
  alphaPersistFactor?: number;
  animateColor?: boolean;
  startColor?: string;
  textColor?: string;
  backgroundColor?: number | string;
  colorCycleInterval?: number;
  supersample?: number;
}

const hexToRgb = (hex: string): [number, number, number] => {
  let h = hex.replace("#", "");
  if (h.length === 3)
    h = h
      .split("")
      .map((c) => c + c)
      .join("");
  const n = parseInt(h, 16);
  return [(n >> 16) & 255, (n >> 8) & 255, n & 255];
};

const loadFont = async (fam: string) => {
  if ("fonts" in document) await (document as any).fonts.load(`16px ${fam}`);
};

const TextTrail: React.FC<TextTrailProps> = ({
  text = "Vibe",
  fontFamily = "Figtree",
  fontWeight = "900",
  noiseFactor = 1,
  noiseScale = 0.0005,
  rgbPersistFactor = 0.98,
  alphaPersistFactor = 0.95,
  animateColor = false,
  startColor = "#ffffff",
  textColor = "#ffffff",
  backgroundColor = 0x271e37,
  colorCycleInterval = 3000,
  supersample = 2,
}) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current) return;

    const { width, height } = ref.current.getBoundingClientRect();
    if (width === 0 || height === 0) return;

    let renderer: WebGLRenderer;
    let scene: Scene;
    let fluidScene: Scene;
    let cam: OrthographicCamera;
    let quad: Mesh;
    let label: Mesh;
    let quadMat: ShaderMaterial;
    let labelMat: ShaderMaterial;
    let rt0: WebGLRenderTarget;
    let rt1: WebGLRenderTarget;
    let clock: Clock;

    const mouse = [0, 0];
    const target = [0, 0];
    const persistColor = useRef([1, 1, 1]);
    const targetColor = useRef([1, 1, 1]);

    const init = async () => {
      await loadFont(fontFamily);

      // Initialize Three.js components
      renderer = new WebGLRenderer({ alpha: true, antialias: false });
      renderer.setSize(width, height);
      renderer.setPixelRatio(window.devicePixelRatio);
      ref.current?.appendChild(renderer.domElement);

      scene = new Scene();
      fluidScene = new Scene();
      clock = new Clock();

      const w = width;
      const h = height;
      cam = new OrthographicCamera(-w / 2, w / 2, h / 2, -h / 2, 0.1, 1000);
      cam.position.z = 1;

      // Create render targets
      rt0 = new WebGLRenderTarget(w, h, {
        minFilter: LinearFilter,
        magFilter: LinearFilter,
        generateMipmaps: false,
      });
      rt1 = new WebGLRenderTarget(w, h, {
        minFilter: LinearFilter,
        magFilter: LinearFilter,
        generateMipmaps: false,
      });

      // Create text texture
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d")!;
      canvas.width = w * supersample;
      canvas.height = h * supersample;
      ctx.scale(supersample, supersample);

      ctx.fillStyle = typeof backgroundColor === "string" ? backgroundColor : `#${backgroundColor.toString(16).padStart(6, "0")}`;
      ctx.fillRect(0, 0, w, h);

      ctx.font = `${fontWeight} ${Math.min(w, h) * 0.8}px ${fontFamily}`;
      ctx.fillStyle = textColor || startColor;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(text, w / 2, h / 2);

      const texture = new CanvasTexture(canvas);
      texture.minFilter = LinearMipmapLinearFilter;
      texture.magFilter = LinearFilter;

      // Shader materials
      const vertexShader = `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `;

      const fragmentShader = `
        uniform sampler2D sampler;
        uniform vec2 mousePos;
        uniform float time;
        uniform float noiseFactor;
        uniform float noiseScale;
        uniform float rgbPersist;
        uniform float alphaPersist;
        varying vec2 vUv;

        float random(vec2 st) {
          return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
        }

        void main() {
          vec2 uv = vUv;
          vec2 noise = vec2(
            random(uv + time * noiseScale) - 0.5,
            random(uv + time * noiseScale + 1000.0) - 0.5
          ) * noiseFactor * 0.01;
          
          vec4 color = texture2D(sampler, uv + noise);
          color.rgb *= rgbPersist;
          color.a *= alphaPersist;
          
          gl_FragColor = color;
        }
      `;

      quadMat = new ShaderMaterial({
        uniforms: {
          sampler: { value: rt1.texture },
          mousePos: { value: new Vector2() },
          time: { value: 0 },
          noiseFactor: { value: noiseFactor },
          noiseScale: { value: noiseScale },
          rgbPersist: { value: rgbPersistFactor },
          alphaPersist: { value: alphaPersistFactor },
        },
        vertexShader,
        fragmentShader,
      });

      labelMat = new ShaderMaterial({
        uniforms: {
          color: { value: new Vector3(...persistColor.current) },
        },
        vertexShader: `
          varying vec2 vUv;
          void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform vec3 color;
          uniform sampler2D map;
          varying vec2 vUv;
          void main() {
            vec4 texColor = texture2D(map, vUv);
            gl_FragColor = vec4(color * texColor.rgb, texColor.a);
          }
        `,
        map: texture,
        transparent: true,
      });

      quad = new Mesh(new PlaneGeometry(w, h), quadMat);
      label = new Mesh(new PlaneGeometry(Math.min(w, h), Math.min(w, h)), labelMat);

      fluidScene.add(quad);
      scene.add(label);

      // Mouse tracking
      const onMove = (e: PointerEvent) => {
        const rect = ref.current?.getBoundingClientRect();
        if (!rect) return;
        target[0] = ((e.clientX - rect.left) / rect.width) * 2 - 1;
        target[1] = -((e.clientY - rect.top) / rect.height) * 2 + 1;
      };

      ref.current?.addEventListener("pointermove", onMove);

      // Color cycling
      if (animateColor && !textColor) {
        const [r, g, b] = hexToRgb(startColor);
        persistColor.current = [r / 255, g / 255, b / 255];
        targetColor.current = [r / 255, g / 255, b / 255];
      }

      // Resize observer
      const ro = new ResizeObserver((entries) => {
        if (!entries[0] || !quad || !label) return;
        const { width: w, height: h } = entries[0].contentRect;
        renderer.setSize(w, h);
        cam.left = -w / 2;
        cam.right = w / 2;
        cam.top = h / 2;
        cam.bottom = -h / 2;
        cam.updateProjectionMatrix();
        quad.geometry.dispose();
        quad.geometry = new PlaneGeometry(w, h);
        rt0.setSize(w, h);
        rt1.setSize(w, h);
        label.geometry.dispose();
        label.geometry = new PlaneGeometry(Math.min(w, h), Math.min(w, h));
      });
      ro.observe(ref.current);

      const timer = setInterval(() => {
        if (!textColor) {
          targetColor.current = [Math.random(), Math.random(), Math.random()];
        }
      }, colorCycleInterval);

      renderer.setAnimationLoop(() => {
        const dt = clock.getDelta();
        if (animateColor && !textColor) {
          for (let i = 0; i < 3; i++)
            persistColor.current[i] +=
              (targetColor.current[i] - persistColor.current[i]) * dt;
        }

        const speed = dt * 5;
        mouse[0] += (target[0] - mouse[0]) * speed;
        mouse[1] += (target[1] - mouse[1]) * speed;

        quadMat.uniforms.mousePos.value.set(mouse[0], mouse[1]);
        quadMat.uniforms.sampler.value = rt1.texture;
        quadMat.uniforms.time.value = clock.getElapsedTime();
        labelMat.uniforms.color.value.set(...persistColor.current);

        renderer.autoClearColor = false;
        renderer.setRenderTarget(rt0);
        renderer.clearColor();
        renderer.render(fluidScene, cam);
        renderer.render(scene, cam);
        renderer.setRenderTarget(null);
        renderer.render(fluidScene, cam);
        renderer.render(scene, cam);

        [rt0, rt1] = [rt1, rt0];
      });

      return () => {
        renderer.setAnimationLoop(null);
        clearInterval(timer);
        ref.current?.removeEventListener("pointermove", onMove);
        ro.disconnect();
        // eslint-disable-next-line react-hooks/exhaustive-deps
        ref.current?.removeChild(renderer.domElement);
        renderer.dispose();
        rt0.dispose();
        rt1.dispose();
        quadMat.dispose();
        quad.geometry.dispose();
        labelMat.dispose();
        label.geometry.dispose();
      };
    };

    init();
  }, [
    text,
    fontFamily,
    fontWeight,
    noiseFactor,
    noiseScale,
    rgbPersistFactor,
    alphaPersistFactor,
    animateColor,
    startColor,
    textColor,
    backgroundColor,
    colorCycleInterval,
    supersample,
  ]);

  return <div ref={ref} className="text-trail" />;
};

export default TextTrail;
