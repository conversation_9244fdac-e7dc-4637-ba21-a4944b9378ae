import React from 'react';
import { Card } from '../../../components';
import styles from './CustomerReviewBlock.module.css';

interface CustomerReviewBlockProps {
  customerName: string;
  reviewText: string;
  rating: number;
  source: string;
  location?: string;
  date?: string;
  className?: string;
}

const CustomerReviewBlock: React.FC<CustomerReviewBlockProps> = ({
  customerName,
  reviewText,
  rating,
  source,
  location,
  date,
  className
}) => {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <span key={i} className={styles.star} aria-hidden="true">
          ★
        </span>
      );
    }
    
    if (hasHalfStar) {
      stars.push(
        <span key="half" className={styles.starHalf} aria-hidden="true">
          ☆
        </span>
      );
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <span key={`empty-${i}`} className={styles.starEmpty} aria-hidden="true">
          ☆
        </span>
      );
    }
    
    return stars;
  };

  return (
    <Card 
      variant="outlined" 
      padding="large" 
      className={`${styles.reviewBlock} ${className || ''}`}
    >
      <div className={styles.header}>
        <div className={styles.rating} role="img" aria-label={`${rating} out of 5 stars`}>
          {renderStars(rating)}
        </div>
        <div className={styles.source}>
          {source}
          {location && ` - ${location}`}
        </div>
      </div>
      
      <blockquote className={styles.reviewText}>
        "{reviewText}"
      </blockquote>
      
      <div className={styles.footer}>
        <cite className={styles.customerName}>
          — {customerName}
        </cite>
        {date && (
          <time className={styles.date} dateTime={date}>
            {new Date(date).toLocaleDateString()}
          </time>
        )}
      </div>
    </Card>
  );
};

export default CustomerReviewBlock;
